import React, { useState } from 'react';
import TechnicianDashboard from './TechnicianDashboard';

const API_BASE_URL = 'http://localhost:4000';

const App = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ text: '', type: '' });

  const handleLogin = async () => {
    if (!email || !password) {
      alert('Erreur: Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });
      
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        setUser(data.user);
        setIsLoggedIn(true);

        alert(`Connexion réussie!\nBienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}`);

        // Vérifier le rôle et rediriger
        if (data.user.role === 'Tech') {
          console.log('👨‍🔧 Accès autorisé - Technicien');
        } else if (data.user.role === 'Admin') {
          console.log('👨‍💼 Accès autorisé - Administrateur');
        } else {
          Alert.alert('Erreur', 'Rôle utilisateur non autorisé');
          return;
        }
      } else {
        Alert.alert('Erreur de connexion', data.message || 'Erreur de connexion');
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      Alert.alert(
        'Erreur de connexion',
        'Erreur de connexion au serveur. Vérifiez que le serveur est démarré sur le port 4000.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUser(null);
    setEmail('');
    setPassword('');
    showMessage('', '');
  };

  const showMessage = (text, type) => {
    setMessage({ text, type });
  };



  // Si l'utilisateur est connecté, afficher le dashboard
  if (isLoggedIn && user) {
    return <TechnicianDashboard user={user} onLogout={handleLogout} />;
  }

  // Sinon, afficher la page de connexion
  return (
    <div style={styles.body}>
      <div style={styles.authContainer}>
        <div style={styles.logo}>
          <h1 style={styles.logoTitle}>AquaTrack</h1>
          <p style={styles.logoSubtitle}>Système de Facturation</p>
        </div>

        {message.text && (
          <div style={{
            ...styles.message,
            ...styles[message.type]
          }}>
            {message.text}
          </div>
        )}

        {loading && (
          <div style={styles.loading}>
            Connexion en cours...
          </div>
        )}

        <form onSubmit={handleLogin} style={styles.form}>
          <div style={styles.formGroup}>
            <label style={styles.label}>Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              style={styles.input}
              required
              disabled={loading}
            />
          </div>

          <div style={styles.formGroup}>
            <label style={styles.label}>Mot de passe</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              style={styles.input}
              required
              disabled={loading}
            />
          </div>

          <button 
            type="submit" 
            style={{
              ...styles.loginBtn,
              ...(loading ? styles.loginBtnDisabled : {})
            }}
            disabled={loading}
          >
            {loading ? 'Connexion...' : 'Se connecter'}
          </button>
        </form>


      </div>
    </div>
  );
};

const styles = {
  body: {
    fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 0,
    padding: 0,
  },
  authContainer: {
    background: 'white',
    padding: '40px',
    borderRadius: '15px',
    boxShadow: '0 15px 35px rgba(0, 0, 0, 0.1)',
    width: '100%',
    maxWidth: '400px',
  },
  logo: {
    textAlign: 'center',
    marginBottom: '30px',
  },
  logoTitle: {
    color: '#2196F3',
    fontSize: '32px',
    fontWeight: 'bold',
    marginBottom: '8px',
    margin: '0 0 8px 0',
  },
  logoSubtitle: {
    color: '#666',
    fontSize: '16px',
    margin: 0,
  },
  form: {
    marginBottom: '20px',
  },
  formGroup: {
    marginBottom: '20px',
  },
  label: {
    display: 'block',
    marginBottom: '8px',
    color: '#333',
    fontWeight: '600',
  },
  input: {
    width: '100%',
    padding: '12px 15px',
    border: '2px solid #ddd',
    borderRadius: '8px',
    fontSize: '16px',
    transition: 'border-color 0.3s',
    boxSizing: 'border-box',
  },
  loginBtn: {
    width: '100%',
    padding: '15px',
    background: '#2196F3',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    fontSize: '18px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'background 0.3s',
    marginBottom: '20px',
  },
  loginBtnDisabled: {
    background: '#ccc',
    cursor: 'not-allowed',
  },

  message: {
    padding: '15px',
    borderRadius: '8px',
    marginBottom: '20px',
    display: 'block',
  },
  success: {
    background: '#d4edda',
    color: '#155724',
    border: '1px solid #c3e6cb',
  },
  error: {
    background: '#f8d7da',
    color: '#721c24',
    border: '1px solid #f5c6cb',
  },
  loading: {
    textAlign: 'center',
    color: '#666',
    marginBottom: '20px',
  },
};

export default App;
